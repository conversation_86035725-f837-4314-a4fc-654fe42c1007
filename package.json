{"name": "tradai-gemini-analyzer", "version": "2.0.0", "description": "Streamlined Gemini AI-powered trading chart analysis application", "main": "pages/index.tsx", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@types/node": "20.14.12", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "10.4.19", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "dotenv": "^17.2.0", "file-type": "^21.0.0", "formidable": "^3.5.4", "framer-motion": "11.3.19", "lucide-react": "0.408.0", "next": "14.2.5", "postcss": "8.4.40", "react": "18.3.1", "react-dom": "18.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.7", "typescript": "5.5.4"}, "devDependencies": {"eslint": "8.57.0", "eslint-config-next": "14.2.5"}, "engines": {"node": ">=18.0.0"}}